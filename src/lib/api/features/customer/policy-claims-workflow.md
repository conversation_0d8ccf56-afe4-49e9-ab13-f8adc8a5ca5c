# Policy Claims API Workflow Configuration

## Overview

This document describes the JSON configuration file `policy-claims-workflow.json` that defines the complete API integration workflow for fetching insurance policies and claims data from the TPA (Third Party Administrator) API.

## Purpose

The configuration file was extracted from the `PolicyClaimsApiStepsSection.svelte` component to create a backend-consumable workflow definition that maintains all the logic and data dependencies while being suitable for automated execution.

## File Location

```
src/lib/api/features/customer/policy-claims-workflow.json
```

## Configuration Structure

### 1. Workflow Metadata
- **name**: Human-readable workflow name
- **description**: Detailed description of the workflow purpose
- **version**: Configuration version for tracking changes
- **created_date**: When the configuration was created
- **documentation_reference**: Source component reference

### 2. Database Prerequisites
Defines database operations required before starting the API workflow:
- Reading `SOCIAL_ID` and `CHANNEL_ID` from `customer_customerplatformidentity` table
- These values are used throughout the API calls

### 3. Workflow Steps
Four sequential steps with detailed configuration:

#### Step 1: Get Bearer Token
- **Purpose**: Authenticate with TPA API
- **Dependencies**: None (first step)
- **Input**: Database values + hardcoded credentials
- **Output**: JWT bearer token for subsequent API calls
- **Token Expiry**: 1 hour (can be reused for multiple calls)

#### Step 2: Verify Citizen ID
- **Purpose**: Validate customer's Citizen ID
- **Dependencies**: Bearer token from Step 1
- **Input**: Database values + bearer token
- **Output**: Verified CitizenID for policy lookup
- **Validation**: Status must equal '1' for success

#### Step 3: Fetch Policy List
- **Purpose**: Retrieve customer's insurance policies
- **Dependencies**: Bearer token + verified CitizenID
- **Input**: Database values + bearer token + CitizenID from Step 2
- **Output**: Array of policies with MemberCodes
- **Note**: Each policy contains a unique MemberCode needed for Step 4

#### Step 4: Fetch Policy Details (Iterative)
- **Purpose**: Get detailed policy and claims data
- **Dependencies**: Bearer token + MemberCodes from Step 3
- **Execution**: Iterates over each MemberCode from Step 3
- **Input**: Database values + bearer token + individual MemberCode
- **Output**: Detailed policy information and claims data

### 4. Data Flow and Dependencies

```
Database → Step 1 → Bearer Token
    ↓         ↓
Step 2 → CitizenID
    ↓         ↓
Step 3 → MemberCodes[]
    ↓         ↓
Step 4 → Policy Details (for each MemberCode)
```

### 5. Post-Execution Operations
- **Database Storage**: Upsert data into `CustomerPolicies` table
- **Primary Key**: Composite key using `customer_id` and `PolNo`
- **Conflict Resolution**: Update only if data has changed
- **Cleanup**: Remove temporary data like bearer tokens

### 6. Error Handling
- **Step-level retries**: Each step has configurable retry attempts
- **Global timeout**: 10-minute maximum workflow execution time
- **Failure actions**: Configurable per step (abort, continue, etc.)
- **Error logging**: Comprehensive error tracking with request/response details

### 7. Monitoring and Performance
- **Execution tracking**: Monitor total workflow time and individual step times
- **Performance thresholds**: Alert if steps exceed expected duration
- **API response monitoring**: Track response times for performance optimization

## Template Variables

The configuration uses template variables for dynamic data injection:

- `{{database.social_id}}` - SOCIAL_ID from database
- `{{database.channel_id}}` - CHANNEL_ID from database
- `{{step_1.bearer_token}}` - Bearer token from Step 1
- `{{step_2.citizen_id}}` - CitizenID from Step 2
- `{{step_3.member_codes}}` - Array of MemberCodes from Step 3
- `{{iteration.current_member_code}}` - Current MemberCode in Step 4 iteration
- `{{input.customer_id}}` - Customer ID passed to workflow
- `{{current_timestamp}}` - Current timestamp for database operations

## Backend Implementation Requirements

To implement this workflow, the backend should:

1. **Parse the JSON configuration** and validate the structure
2. **Execute database prerequisites** before starting API calls
3. **Process steps sequentially** respecting dependencies
4. **Handle template variable substitution** for dynamic data
5. **Implement retry logic** as specified in error handling
6. **Support iterative execution** for Step 4's multiple MemberCode calls
7. **Perform post-execution operations** including database storage
8. **Provide monitoring and logging** as specified

## Security Considerations

- **Credentials**: The USERNAME and PASSWORD are hardcoded in the configuration
- **Token Management**: Bearer tokens should be securely handled and not logged
- **Database Access**: Ensure proper access controls for customer data
- **API Endpoints**: All endpoints use HTTPS for secure communication

## Maintenance

- **Version Control**: Update the version number when making changes
- **Documentation**: Keep this documentation in sync with configuration changes
- **Testing**: Test workflow changes in UAT environment before production
- **Monitoring**: Review performance metrics regularly for optimization opportunities

## Related Files

- **Source Component**: `src/lib/components/settings/business/PolicyClaimsApiStepsSection.svelte`
- **Customer Services**: `src/lib/api/features/customer/customers.service.ts`
- **API Types**: `src/lib/api/types/customer.ts`
