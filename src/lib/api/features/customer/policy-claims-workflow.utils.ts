/**
 * Utility functions for Policy Claims API Workflow Configuration
 */

import type { 
  PolicyClaimsWorkflowConfig, 
  WorkflowValidationResult,
  TemplateVariable,
  WorkflowExecutionContext
} from './policy-claims-workflow.types';

// Import the workflow configuration
import workflowConfig from './policy-claims-workflow.json';

/**
 * Load the workflow configuration with type safety
 */
export function loadWorkflowConfig(): PolicyClaimsWorkflowConfig {
  return workflowConfig as PolicyClaimsWorkflowConfig;
}

/**
 * Validate the workflow configuration structure
 */
export function validateWorkflowConfig(config: PolicyClaimsWorkflowConfig): WorkflowValidationResult {
  const errors: string[] = [];
  const warnings: string[] = [];

  // Validate workflow metadata
  if (!config.workflow?.name) {
    errors.push('Workflow name is required');
  }
  if (!config.workflow?.version) {
    errors.push('Workflow version is required');
  }

  // Validate database prerequisites
  if (!config.database_prerequisites?.operations?.length) {
    warnings.push('No database prerequisites defined');
  }

  // Validate workflow steps
  if (!config.workflow_steps?.length) {
    errors.push('At least one workflow step is required');
  } else {
    // Check step sequence and dependencies
    const stepIds = config.workflow_steps.map(step => step.step_id);
    const stepNames = config.workflow_steps.map(step => step.step_name);

    // Validate step IDs are sequential
    const sortedSteps = [...config.workflow_steps].sort((a, b) => a.sequence_order - b.sequence_order);
    for (let i = 0; i < sortedSteps.length; i++) {
      if (sortedSteps[i].sequence_order !== i + 1) {
        errors.push(`Step sequence order should be sequential. Expected ${i + 1}, got ${sortedSteps[i].sequence_order}`);
      }
    }

    // Validate dependencies exist
    config.workflow_steps.forEach(step => {
      step.dependencies.forEach(dep => {
        if (!stepNames.includes(dep)) {
          errors.push(`Step "${step.step_name}" depends on non-existent step "${dep}"`);
        }
      });
    });

    // Validate API configurations
    config.workflow_steps.forEach(step => {
      if (!step.api_configuration?.endpoint) {
        errors.push(`Step "${step.step_name}" missing API endpoint`);
      }
      if (!step.api_configuration?.method) {
        errors.push(`Step "${step.step_name}" missing HTTP method`);
      }
    });
  }

  // Validate post-execution operations
  if (!config.post_execution?.database_operations?.length) {
    warnings.push('No post-execution database operations defined');
  }

  return {
    valid: errors.length === 0,
    errors,
    warnings
  };
}

/**
 * Extract template variables from a string
 */
export function extractTemplateVariables(text: string): string[] {
  const templateRegex = /\{\{([^}]+)\}\}/g;
  const variables: string[] = [];
  let match;

  while ((match = templateRegex.exec(text)) !== null) {
    variables.push(match[1]);
  }

  return variables;
}

/**
 * Resolve template variables in a string using execution context
 */
export function resolveTemplateVariables(
  text: string, 
  context: WorkflowExecutionContext
): string {
  return text.replace(/\{\{([^}]+)\}\}/g, (match, variable) => {
    const parts = variable.split('.');
    
    if (parts[0] === 'database') {
      return context.database_data[parts[1]] || match;
    }
    
    if (parts[0].startsWith('step_')) {
      const stepNumber = parts[0].replace('step_', '');
      const stepData = context.step_data[`step_${stepNumber}`];
      return stepData?.[parts[1]] || match;
    }
    
    if (parts[0] === 'input') {
      return (context as any)[parts[1]] || match;
    }
    
    if (variable === 'current_timestamp') {
      return new Date().toISOString();
    }
    
    return match; // Return original if not found
  });
}

/**
 * Get step by ID
 */
export function getStepById(config: PolicyClaimsWorkflowConfig, stepId: number) {
  return config.workflow_steps.find(step => step.step_id === stepId);
}

/**
 * Get step by name
 */
export function getStepByName(config: PolicyClaimsWorkflowConfig, stepName: string) {
  return config.workflow_steps.find(step => step.step_name === stepName);
}

/**
 * Get steps in execution order
 */
export function getStepsInOrder(config: PolicyClaimsWorkflowConfig) {
  return [...config.workflow_steps].sort((a, b) => a.sequence_order - b.sequence_order);
}

/**
 * Check if a step has all its dependencies satisfied
 */
export function areStepDependenciesSatisfied(
  step: any,
  completedSteps: string[]
): boolean {
  return step.dependencies.every((dep: string) => completedSteps.includes(dep));
}

/**
 * Get the next executable step
 */
export function getNextExecutableStep(
  config: PolicyClaimsWorkflowConfig,
  completedSteps: string[]
) {
  const stepsInOrder = getStepsInOrder(config);
  
  return stepsInOrder.find(step => 
    !completedSteps.includes(step.step_name) &&
    areStepDependenciesSatisfied(step, completedSteps)
  );
}

/**
 * Create execution context for workflow
 */
export function createExecutionContext(customerId: string): WorkflowExecutionContext {
  return {
    customer_id: customerId,
    execution_id: `exec_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
    started_at: new Date(),
    current_step: 0,
    step_data: {},
    database_data: {}
  };
}

/**
 * Validate template variables in configuration
 */
export function validateTemplateVariables(config: PolicyClaimsWorkflowConfig): string[] {
  const errors: string[] = [];
  const availableVariables = new Set<string>();
  
  // Add database variables
  config.database_prerequisites.operations.forEach(op => {
    if (op.store_as) {
      Object.keys(op.store_as).forEach(key => {
        availableVariables.add(`database.${key}`);
      });
    }
  });
  
  // Process steps in order to track available variables
  const stepsInOrder = getStepsInOrder(config);
  
  stepsInOrder.forEach(step => {
    // Check request body template variables
    const requestBodyStr = JSON.stringify(step.api_configuration.request_body || {});
    const usedVariables = extractTemplateVariables(requestBodyStr);
    
    usedVariables.forEach(variable => {
      if (!availableVariables.has(variable) && 
          variable !== 'current_timestamp' && 
          !variable.startsWith('input.') &&
          !variable.startsWith('iteration.')) {
        errors.push(`Step "${step.step_name}" uses undefined variable: ${variable}`);
      }
    });
    
    // Add variables that this step produces
    if (step.response_handling.data_extraction) {
      Object.keys(step.response_handling.data_extraction).forEach(key => {
        availableVariables.add(`step_${step.step_id}.${key}`);
      });
    }
  });
  
  return errors;
}

/**
 * Get workflow summary for logging/monitoring
 */
export function getWorkflowSummary(config: PolicyClaimsWorkflowConfig) {
  return {
    name: config.workflow.name,
    version: config.workflow.version,
    total_steps: config.workflow_steps.length,
    estimated_duration_minutes: config.error_handling.global_timeout_minutes,
    database_operations: config.database_prerequisites.operations.length,
    post_execution_operations: config.post_execution.database_operations.length
  };
}
