{"workflow": {"name": "Policy Claims API Integration", "description": "Complete workflow for fetching insurance policies and claims data from TPA API", "version": "1.0.0", "created_date": "2024-09-14", "documentation_reference": "PolicyClaimsApiStepsSection.svelte"}, "database_prerequisites": {"description": "Database operations required before starting the API workflow", "operations": [{"operation": "read", "table": "customer_customerplatformidentity", "fields": ["platform_user_id", "channel_id"], "where_condition": "customer_id = :customer_id", "purpose": "Get SOCIAL_ID and CHANNEL_ID for API authentication", "store_as": {"social_id": "platform_user_id", "channel_id": "channel_id"}}]}, "workflow_steps": [{"step_id": 1, "step_name": "get_bearer_token", "title": "<PERSON> <PERSON>", "description": "Retrieve authentication token from TPA API", "sequence_order": 1, "dependencies": [], "api_configuration": {"endpoint": "https://uat.thirdpartyadmin.co.th:4443/TPA.TMS.Web.API_PREPROV2/api/GetToken", "method": "POST", "headers": {"Content-Type": "application/json"}, "request_body": {"USERNAME": "BVTPA", "PASSWORD": "*d!n^+Cb@1", "SOCIAL_ID": "{{database.social_id}}", "CHANNEL_ID": "{{database.channel_id}}", "CHANNEL": "LINE"}, "timeout_seconds": 30}, "response_handling": {"expected_format": "string", "success_indicators": ["response is not empty", "response is valid JWT token"], "data_extraction": {"bearer_token": {"path": "$", "transform": "remove_quotes", "store_for_subsequent_steps": true, "description": "JWT token for API authentication"}}}, "error_handling": {"retry_attempts": 3, "retry_delay_seconds": 5, "failure_action": "abort_workflow"}}, {"step_id": 2, "step_name": "verify_citizen_id", "title": "Verify Citizen ID", "description": "Validate Citizen ID with external API", "sequence_order": 2, "dependencies": ["get_bearer_token"], "api_configuration": {"endpoint": "https://uat.thirdpartyadmin.co.th:4443/TPA.TMS.Web.API_PREPROV2/api/SearchCitizenID", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {{step_1.bearer_token}}"}, "request_body": {"SOCIAL_ID": "{{database.social_id}}", "CHANNEL_ID": "{{database.channel_id}}", "CHANNEL": "LINE"}, "timeout_seconds": 30}, "response_handling": {"expected_format": "json", "success_indicators": ["ListOfSearchCitizenID exists", "Status equals '1'"], "data_extraction": {"citizen_id": {"path": "$.ListOfSearchCitizenID[0].CitizenID", "required": true, "store_for_subsequent_steps": true, "description": "Verified Citizen ID for policy lookup"}, "verification_status": {"path": "$.ListOfSearchCitizenID[0].Status", "required": true, "validation": "equals_1"}}}, "error_handling": {"retry_attempts": 2, "retry_delay_seconds": 3, "failure_action": "abort_workflow", "error_conditions": [{"condition": "Status != '1'", "message": "Citizen ID verification failed"}, {"condition": "CitizenID is empty", "message": "No Citizen ID found in response"}]}}, {"step_id": 3, "step_name": "fetch_policy_list", "title": "Fetch Policy List", "description": "Retrieve customer policy list using verified Citizen ID", "sequence_order": 3, "dependencies": ["get_bearer_token", "verify_citizen_id"], "api_configuration": {"endpoint": "https://uat.thirdpartyadmin.co.th:4443/TPA.TMS.Web.API_PREPROV2/api/PolicyListSocial", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {{step_1.bearer_token}}"}, "request_body": {"SOCIAL_ID": "{{database.social_id}}", "CHANNEL_ID": "{{database.channel_id}}", "CHANNEL": "LINE", "CITIZEN_ID": "{{step_2.citizen_id}}"}, "timeout_seconds": 45}, "response_handling": {"expected_format": "json", "success_indicators": ["ListOfPolicyListSocial exists", "array is not empty"], "data_extraction": {"policies": {"path": "$.ListOfPolicyListSocial", "type": "array", "required": true, "store_for_subsequent_steps": true, "description": "Array of customer policies"}, "member_codes": {"path": "$.ListOfPolicyListSocial[*].MemberCode", "type": "array", "required": true, "store_for_subsequent_steps": true, "description": "Member codes for detailed policy retrieval"}}}, "error_handling": {"retry_attempts": 2, "retry_delay_seconds": 5, "failure_action": "continue_with_empty_result", "error_conditions": [{"condition": "ListOfPolicyListSocial is empty", "message": "No policies found for this customer"}]}}, {"step_id": 4, "step_name": "fetch_policy_details", "title": "Fetch Policy & Claims Details", "description": "Get detailed policy and claims data for each MemberCode", "sequence_order": 4, "dependencies": ["get_bearer_token", "fetch_policy_list"], "execution_type": "iterative", "iteration_source": "{{step_3.member_codes}}", "api_configuration": {"endpoint": "https://uat.thirdpartyadmin.co.th:4443/TPA.TMS.Web.API_PREPROV2/api/PolicyDetailSocial", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {{step_1.bearer_token}}"}, "request_body": {"SOCIAL_ID": "{{database.social_id}}", "CHANNEL_ID": "{{database.channel_id}}", "CHANNEL": "LINE", "MEMBER_CODE": "{{iteration.current_member_code}}"}, "timeout_seconds": 60}, "response_handling": {"expected_format": "json", "success_indicators": ["response contains policy or claims data"], "data_extraction": {"policy_details": {"path": "$.ListOfPolDet", "required": false, "description": "Detailed policy information"}, "claims_data": {"path": "$.ListOfPolClaim", "required": false, "description": "Claims history and status"}}, "aggregation": {"type": "collect_all", "store_as": "all_policy_details"}}, "error_handling": {"retry_attempts": 2, "retry_delay_seconds": 5, "failure_action": "continue_to_next_iteration", "error_conditions": [{"condition": "timeout", "message": "Policy details request timed out"}]}}], "post_execution": {"description": "Operations to perform after successful workflow completion", "database_operations": [{"operation": "upsert", "table": "CustomerPolicies", "primary_key": ["customer_id", "PolNo"], "data_source": "workflow_results", "mapping": {"customer_id": "{{input.customer_id}}", "citizen_id": "{{step_2.citizen_id}}", "policies_data": "{{step_3.policies}}", "policy_details": "{{step_4.all_policy_details}}", "last_updated": "{{current_timestamp}}", "sync_status": "completed"}, "conflict_resolution": "update_if_changed"}], "cleanup_operations": [{"action": "clear_temporary_data", "data": ["bearer_token", "intermediate_responses"]}]}, "error_handling": {"global_timeout_minutes": 10, "global_retry_policy": {"max_attempts": 1, "backoff_strategy": "exponential"}, "failure_notifications": {"log_level": "error", "include_request_details": true, "include_response_details": true}}, "monitoring": {"track_execution_time": true, "track_api_response_times": true, "log_data_extraction": true, "performance_thresholds": {"total_workflow_time_minutes": 5, "individual_step_time_seconds": 60}}}