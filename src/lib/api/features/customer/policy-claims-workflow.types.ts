/**
 * TypeScript interfaces for Policy Claims API Workflow Configuration
 * These types define the structure of the policy-claims-workflow.json file
 */

export interface WorkflowMetadata {
  name: string;
  description: string;
  version: string;
  created_date: string;
  documentation_reference: string;
}

export interface DatabaseOperation {
  operation: 'read' | 'write' | 'upsert' | 'delete';
  table: string;
  fields?: string[];
  where_condition?: string;
  purpose: string;
  store_as?: Record<string, string>;
}

export interface DatabasePrerequisites {
  description: string;
  operations: DatabaseOperation[];
}

export interface ApiConfiguration {
  endpoint: string;
  method: 'GET' | 'POST' | 'PUT' | 'DELETE';
  headers: Record<string, string>;
  request_body?: Record<string, any>;
  timeout_seconds: number;
}

export interface DataExtraction {
  path: string;
  type?: 'string' | 'number' | 'boolean' | 'array' | 'object';
  required?: boolean;
  store_for_subsequent_steps?: boolean;
  description: string;
  transform?: string;
  validation?: string;
}

export interface ResponseHandling {
  expected_format: 'string' | 'json';
  success_indicators: string[];
  data_extraction: Record<string, DataExtraction>;
  aggregation?: {
    type: 'collect_all' | 'merge' | 'sum' | 'count';
    store_as: string;
  };
}

export interface ErrorCondition {
  condition: string;
  message: string;
}

export interface ErrorHandling {
  retry_attempts: number;
  retry_delay_seconds: number;
  failure_action: 'abort_workflow' | 'continue_to_next_step' | 'continue_to_next_iteration' | 'continue_with_empty_result';
  error_conditions?: ErrorCondition[];
}

export interface WorkflowStep {
  step_id: number;
  step_name: string;
  title: string;
  description: string;
  sequence_order: number;
  dependencies: string[];
  execution_type?: 'single' | 'iterative';
  iteration_source?: string;
  api_configuration: ApiConfiguration;
  response_handling: ResponseHandling;
  error_handling: ErrorHandling;
}

export interface DatabasePostOperation {
  operation: 'insert' | 'update' | 'upsert' | 'delete';
  table: string;
  primary_key?: string[];
  data_source: string;
  mapping: Record<string, string>;
  conflict_resolution?: 'update_if_changed' | 'always_update' | 'skip_if_exists';
}

export interface CleanupOperation {
  action: 'clear_temporary_data' | 'delete_files' | 'reset_cache';
  data?: string[];
}

export interface PostExecution {
  description: string;
  database_operations: DatabasePostOperation[];
  cleanup_operations: CleanupOperation[];
}

export interface GlobalRetryPolicy {
  max_attempts: number;
  backoff_strategy: 'linear' | 'exponential' | 'fixed';
}

export interface FailureNotifications {
  log_level: 'debug' | 'info' | 'warn' | 'error';
  include_request_details: boolean;
  include_response_details: boolean;
}

export interface GlobalErrorHandling {
  global_timeout_minutes: number;
  global_retry_policy: GlobalRetryPolicy;
  failure_notifications: FailureNotifications;
}

export interface PerformanceThresholds {
  total_workflow_time_minutes: number;
  individual_step_time_seconds: number;
}

export interface Monitoring {
  track_execution_time: boolean;
  track_api_response_times: boolean;
  log_data_extraction: boolean;
  performance_thresholds: PerformanceThresholds;
}

export interface PolicyClaimsWorkflowConfig {
  workflow: WorkflowMetadata;
  database_prerequisites: DatabasePrerequisites;
  workflow_steps: WorkflowStep[];
  post_execution: PostExecution;
  error_handling: GlobalErrorHandling;
  monitoring: Monitoring;
}

/**
 * Runtime data interfaces for workflow execution
 */
export interface WorkflowExecutionContext {
  customer_id: string;
  execution_id: string;
  started_at: Date;
  current_step: number;
  step_data: Record<string, any>;
  database_data: Record<string, any>;
}

export interface StepExecutionResult {
  step_id: number;
  step_name: string;
  success: boolean;
  execution_time_ms: number;
  extracted_data: Record<string, any>;
  error_message?: string;
  retry_count: number;
}

export interface WorkflowExecutionResult {
  workflow_name: string;
  execution_id: string;
  customer_id: string;
  success: boolean;
  total_execution_time_ms: number;
  started_at: Date;
  completed_at: Date;
  step_results: StepExecutionResult[];
  final_data: Record<string, any>;
  error_message?: string;
}

/**
 * API Response interfaces based on the TPA API documentation
 */
export interface GetTokenResponse {
  token: string;
}

export interface SearchCitizenIDResponse {
  ListOfSearchCitizenID: Array<{
    Status: string;
    CitizenID: string;
  }>;
  ErrorMessage: string;
}

export interface PolicyListSocialResponse {
  ListOfPolicyListSocial: Array<{
    Name: string;
    CitizenID: string;
    PolNo: string;
    MemberCode: string;
    EffFrom: string;
    EffTo: string;
  }>;
}

export interface PolicyDetailSocialResponse {
  ListOfPolDet: any; // Policy details structure may vary
  ListOfPolClaim: any; // Claims data structure may vary
}

/**
 * Utility type for template variable resolution
 */
export type TemplateVariable = 
  | `{{database.${string}}}`
  | `{{step_${number}.${string}}}`
  | `{{iteration.${string}}}`
  | `{{input.${string}}}`
  | `{{current_timestamp}}`;

/**
 * Configuration validation interface
 */
export interface WorkflowValidationResult {
  valid: boolean;
  errors: string[];
  warnings: string[];
}
